<template>
    <view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="对标分析" />
        </view>
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <!-- 主标签页切换 -->
                <view class="tab-container" v-if="mainTabs.length > 0">
                    <view v-for="(tab, index) in mainTabs" :key="tab.id || index" class="tab-item"
                        :class="{ active: currentMainTab === index }" @click="switchMainTab(index)">
                        {{ tab.name }}
                    </view>
                </view>

                <!-- 加载状态提示 -->
                <view v-else class="loading-tabs">
                    <text class="loading-text">正在加载标签页...</text>
                </view>

                <!-- 对标企业配置区域 -->
                <view class="benchmark-card">
                    <view class="card-header">
                        <view class="bond-title">
                            <view class="title-icon"></view>
                            <text class="title-text">对标企业配置</text>
                        </view>
                        <view class="add-btn" @click="addCompany">
                            <uni-icons type="plus" size="26" color="#FF9900"></uni-icons>
                            <text>添加企业</text>
                        </view>
                    </view>

                    <view class="company-list">
                        <view v-for="(item, index) in currentCompanyList" :key="item.id || index" class="company-item">
                            <view class="company-content">
                                <text class="company-name">{{ item.bmEntName || item.entName || '未知企业' }}</text>
                            </view>
                            <view class="delete-icon" @click="deleteCompany(index)">
                                <uni-icons type="minus" size="16" color="#999"></uni-icons>
                            </view>
                        </view>
                        
                        <!-- 无企业数据提示 -->
                        <view v-if="currentCompanyList.length === 0" class="no-company-tip">
                            <text class="no-company-text">当前分组暂无配置企业</text>
                        </view>
                    </view>
                </view>

                <!-- 底部标签页 -->
                <view class="bottom-tabs">
                    <view v-for="(tab, index) in bottomTabs" :key="index" class="bottom-tab-item"
                        :class="{ active: currentBottomTab === index }" @click="switchBottomTab(index)">
                        {{ tab.name }}
                        <view class="tab-arrow" v-if="currentBottomTab === index"></view>
                    </view>
                </view>

                <!-- 图表区域 -->
                <view class="chart-card">
                    <!-- 分析类型切换 -->
                    <view class="analysis-tab-container" v-if="shouldShowAnalysisTabs">
                        <view v-for="(tab, index) in analysisTabs" :key="index" class="analysis-tab-item"
                            :class="{ active: currentAnalysisTab === index }" @click="switchAnalysisTab(index)">
                            {{ tab.name }}
                        </view>
                    </view>

                    <!-- 筛选条件下拉选择区 -->
                    <view class="filter-dropdown-container">
                        <view v-for="(item, index) in currentFilterOptions" :key="index" class="filter-dropdown-item"
                            @click="toggleDropdown(index)">
                            <text>{{ item.name }}</text>
                            <text class="dropdown-icon">▼</text>
                        </view>
                    </view>

                    <!-- 条件单选区域(可横向滚动) -->
                    <scroll-view class="condition-scroll" scroll-x :show-scrollbar="false"
                        v-if="shouldShowAnalysisTabs">
                        <view class="condition-container">
                            <view v-for="(item, index) in currentConditionItems" :key="index" class="condition-item"
                                :class="{ active: currentCondition === index }" @click="selectCondition(index)">
                                {{ item.name }}
                            </view>
                        </view>
                    </scroll-view>

                    <!-- 图表容器 -->
                    <view class="chart-container">
                        <MixedStackChart ref="mixedChart" :barData="mockChartBarData" :lineData="mockChartLineData"
                            :xLabels="mockChartXLabels" :chartHeight="350" />
                    </view>

                    <view class="chart-desc" v-if="currentBottomTab !== 2">
                        <text class="desc-text">票面加权利率:本企业和对标企业各自存续债券，其票面利率与余额加权平均的利率</text>
                    </view>
                </view>

                <!-- 对标列表 -->
                <view class="benchmark-table-card" v-if="shouldShowBenchmarkList">
                    <view class="table-header">
                        <view class="title-wrapper">
                            <view class="title-icon"></view>
                            <text class="title-text">{{ benchmarkListTitle }}</text>
                        </view>
                    </view>

                    <!-- 债券列表 - 使用SimpleTable组件 -->
                    <view class="bonds-list">
                        <SimpleTable :columns="benchmarkTableColumns" :stripe="false" :data="benchmarkListData"
                            :border="false" :highlight="true" :loading="benchmarkListLoading"
                            :hasMore="benchmarkHasMore" :height="'auto'" @cellClick="showBenchmarkDetail"
                            @loadMore="loadMoreBenchmarkData" :cell-style="benchmarkCellStyle" />

                        <!-- 加载更多按钮 -->
                        <view v-if="benchmarkHasMore && benchmarkListData.length > 0" class="view-more"
                            @click="handleLoadMoreClick">
                            <view class="view-more-content">
                                <image class="icon-arrow" :src="getAssetUrl('/home/<USER>')" mode="">
                                </image>
                                <text class="more-text">查看更多</text>
                            </view>
                        </view>

                        <!-- 无数据提示 -->
                        <view v-if="!benchmarkListData.length && !benchmarkListLoading" class="no-data-tip">
                            <text class="no-data-text">暂无相关数据</text>
                        </view>

                        <!-- 没有更多数据提示 -->
                        <view v-if="!benchmarkHasMore && benchmarkListData.length > 0 && !benchmarkListLoading"
                            class="no-more-tip">
                            <text class="no-more-text">已显示全部数据</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>

        <!-- 对标企业选择弹窗 -->
        <uni-popup ref="companyPopup" type="bottom" :safe-area="false">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">选择对标企业</text>
                    <view class="close-icon" @click="closeCompanyPopup">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="options-header">
                        <text>{{ companyOptions.length > 0 ? '请选择对标企业' : '暂无对标企业数据' }}</text>
                        <view class="options-actions">
                            <text @click="selectAllCompanies">全选</text>
                            <text @click="clearCompanies">清空</text>
                            <text @click="invertCompanies">反选</text>
                        </view>
                    </view>

                    <view class="options-list">
                        <view class="category-options">
                            <view v-for="(item, index) in companyOptions" :key="index" class="option-item"
                                @click="toggleCompanySelection(item)">
                                <view class="checkbox" :class="{ checked: isCompanySelected(item) }">
                                    <uni-icons v-if="isCompanySelected(item)" type="checkmarkempty" size="14"
                                        color="#fff"></uni-icons>
                                </view>
                                <text class="option-label">{{ item.bmEntName }}</text>
                            </view>

                            <!-- 无数据提示 -->
                            <view v-if="companyOptions.length === 0" class="no-data-container">
                                <text class="no-data-text">暂无对标企业数据</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="popup-footer">
                    <button class="confirm-btn" @click="confirmCompanySelection">确认</button>
                </view>
            </view>
        </uni-popup>

        <!-- 债券类型选择弹窗 -->
        <uni-popup ref="bondTypePopup" type="bottom" :safe-area="false">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">债券类型选择</text>
                    <view class="close-icon" @click="closeBondTypePopup">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="options-header">
                        <text>默认勾选全部</text>
                        <view class="options-actions">
                            <text @click="selectAllBondTypes">全选</text>
                            <text @click="clearBondTypes">清空</text>
                            <text @click="invertBondTypes">反选</text>
                        </view>
                    </view>

                    <view class="options-list">
                        <view v-for="(category, categoryIndex) in bondTypeOptions" :key="categoryIndex">
                            <view class="category-title">{{ category.bondTypeName }}</view>
                            <view class="category-options">
                                <view v-for="(subType, index) in category.list" :key="index" class="option-item"
                                    @click="toggleBondTypeSelection(subType)">
                                    <view class="checkbox" :class="{ checked: isBondTypeSelected(subType) }">
                                        <uni-icons v-if="isBondTypeSelected(subType)" type="checkmarkempty" size="14"
                                            color="#fff"></uni-icons>
                                    </view>
                                    <text class="option-label">{{ subType.bondTypeName }}</text>
                                </view>
                            </view>
                        </view>

                        <!-- 无数据提示 -->
                        <view v-if="bondTypeOptions.length === 0" class="no-data-container">
                            <text class="no-data-text">暂无债券类型数据</text>
                        </view>
                    </view>
                </view>
            </view>

            <view class="popup-footer">
                <button class="confirm-btn" @click="confirmBondTypeSelection">确认</button>
            </view>
        </uni-popup>

        <!-- 报告期选择弹窗 -->
        <uni-popup ref="reportPeriodPopup" type="bottom" :safe-area="false">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">选择报告期</text>
                    <view class="close-icon" @click="closeReportPeriodPopup">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="options-header">
                        <text>{{ reportPeriodOptions.length > 0 ? '请选择报告期' : '暂无报告期数据' }}</text>
                    </view>

                    <view class="options-list">
                        <view v-for="(yearGroup, yearIndex) in reportPeriodOptions" :key="yearIndex">
                            <view class="category-title">{{ yearGroup.reportName }}</view>
                            <view class="category-options">
                                <view v-for="(period, index) in yearGroup.list" :key="index" class="option-item"
                                    @click="selectReportPeriod(period)">
                                    <view class="radio" :class="{ checked: isReportPeriodSelected(period) }">
                                        <view v-if="isReportPeriodSelected(period)" class="radio-dot"></view>
                                    </view>
                                    <text class="option-label">{{ period.reportName }}</text>
                                </view>
                            </view>
                        </view>

                        <!-- 无数据提示 -->
                        <view v-if="reportPeriodOptions.length === 0" class="no-data-container">
                            <text class="no-data-text">暂无报告期数据</text>
                        </view>
                    </view>
                </view>

                <view class="popup-footer">
                    <button class="confirm-btn" @click="confirmReportPeriodSelection">确认</button>
                </view>
            </view>
        </uni-popup>

        <!-- 财务指标选择弹窗 -->
        <uni-popup ref="financialIndicatorsPopup" type="bottom" :safe-area="false">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">选择财务指标</text>
                    <view class="close-icon" @click="closeFinancialIndicatorsPopup">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="options-header">
                        <text>{{ financialIndicatorsOptions.length > 0 ? '请选择财务指标（可多选）' : '暂无财务指标数据' }}</text>
                        <view class="options-actions">
                            <text @click="selectAllFinancialIndicators">全选</text>
                            <text @click="clearFinancialIndicators">清空</text>
                            <text @click="invertFinancialIndicators">反选</text>
                        </view>
                    </view>

                    <view class="options-list">
                        <view class="category-options">
                            <view v-for="(indicator, index) in financialIndicatorsOptions" :key="index" class="option-item"
                                @click="toggleFinancialIndicatorSelection(indicator)">
                                <view class="checkbox" :class="{ checked: isFinancialIndicatorSelected(indicator) }">
                                    <uni-icons v-if="isFinancialIndicatorSelected(indicator)" type="checkmarkempty" size="14"
                                        color="#fff"></uni-icons>
                                </view>
                                <text class="option-label">{{ indicator.fieldName }}</text>
                            </view>

                            <!-- 无数据提示 -->
                            <view v-if="financialIndicatorsOptions.length === 0" class="no-data-container">
                                <text class="no-data-text">暂无财务指标数据</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="popup-footer">
                    <button class="confirm-btn" @click="confirmFinancialIndicatorsSelection">确认</button>
                </view>
            </view>
        </uni-popup>

        <!-- 日期选择器组件 -->
        <DateRangePicker v-model:visible="showDatePicker" @dateRangeChange="handleDateRangeChange" />
    </view>
</template>

<script setup>
import CustomHead from '@/components/head/head.vue';
import SimpleTable from '@/components/SimpleTable/index.vue';
import MixedStackChart from '@/components/common/MixedStackChart.vue';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { getBenchmarkAnalysisTab, getBenchmarkCompanyConfigList, getBenchmarkCompanySelectList, getBondType, getFinancialBenchmarkReportPeriod,getFinancialIndicatorsList } from '@/api/benchmarkAnalysis';
import { usePermissionStore } from '@/stores/permission';
import { getAssetUrl } from '@/config/assets';

// 获取财务对标指标选择下拉选择信息
const getFinancialIndicatorsListData = async () => {
    try {
        const res = await getFinancialIndicatorsList({
            entOrgCode: userInfo.orgCode,
            moduleType: 'FINANCIAL_BENCHMARKING'
        });
        console.log('财务对标指标选择下拉选择信息', res.data.data);

        if (res.data && res.data.data && res.data.data.n && res.data.data.n.length > 0) {
            financialIndicatorsOptions.value = res.data.data.n;
            // 默认选择前3个指标（多选）
            if (financialIndicatorsOptions.value.length > 0) {
                const defaultCount = Math.min(3, financialIndicatorsOptions.value.length);
                selectedFinancialIndicators.value = financialIndicatorsOptions.value.slice(0, defaultCount);
            }
        } else {
            financialIndicatorsOptions.value = [];
        }
    } catch (error) {
        console.error('获取财务对标指标选择失败:', error);
        financialIndicatorsOptions.value = [];
        uni.showToast({
            title: '获取指标选择失败',
            icon: 'none'
        });
    }
};

// 获取财务对标报告期下拉选择信息
const getFinancialBenchmarkReportPeriodData = async () => {
    try {
        const res = await getFinancialBenchmarkReportPeriod();
        console.log('财务对标报告期下拉选择信息', res.data.data);

        if (res.data && res.data.data && res.data.data.length > 0) {
            reportPeriodOptions.value = res.data.data;
            // 默认选择第一个年份的第一个报告期
            if (reportPeriodOptions.value[0] && reportPeriodOptions.value[0].list && reportPeriodOptions.value[0].list.length > 0) {
                selectedReportPeriod.value = reportPeriodOptions.value[0].list[0];
            }
        } else {
            reportPeriodOptions.value = [];
        }
    } catch (error) {
        console.error('获取财务对标报告期失败:', error);
        reportPeriodOptions.value = [];
        uni.showToast({
            title: '获取报告期失败',
            icon: 'none'
        });
    }
}

// 监听添加企业事件
const handleAddCompanyToConfig = (eventData) => {
    console.log('收到添加企业事件:', eventData);
    const { company, groupId } = eventData;

    // 检查企业是否已存在
    const existingCompany = allCompanyConfigs.value.find(item =>
        item.groupId === groupId &&
        (item.bmEntId === company.bmEntId || item.id === company.id)
    );

    if (existingCompany) {
        uni.showToast({
            title: '企业已存在',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    // 使用原始企业对象，但需要设置正确的groupId用于分组
    const companyWithGroupId = {
        ...company, // 保留所有原始字段
        groupId: groupId // 设置正确的groupId，确保能正确分组显示
    };

    allCompanyConfigs.value.push(companyWithGroupId);

    console.log('企业添加成功，当前配置列表:', allCompanyConfigs.value);
    console.log('当前分组企业列表:', currentCompanyList.value);

    // 刷新下拉列表选项
    loadCompanyOptions();

    uni.showToast({
        title: '企业添加成功',
        icon: 'success',
        duration: 2000
    });
};

onMounted(async () => {
    // 监听添加企业事件
    uni.$on('addCompanyToConfig', handleAddCompanyToConfig);

    // 页面初始化逻辑：加载所有数据
    await getBenchmarkAnalysisTabData();
    // 加载对标分析标签信息
    await loadAllTabsCompanyConfigData(); // 加载所有tab的企业配置数据
    // 加载下拉列表数据
    loadCompanyOptions();
    // 加载债券类型下拉列表数据
    loadBondTypeOptions();
    // 加载财务对标指标选择下拉列表数据
    getFinancialIndicatorsListData();
    // 加载财务对标报告期下拉列表数据
    getFinancialBenchmarkReportPeriodData();
    setTimeout(() => {
        if (mixedChart.value) {
            mixedChart.value.renderChart();
        }
    }, 500);
});

onUnmounted(() => {
    // 移除事件监听
    uni.$off('addCompanyToConfig', handleAddCompanyToConfig);
});

// ==================== API请求部分 ====================
// 获取对标分析标签信息
const getBenchmarkAnalysisTabData = async () => {
    try {
        const res = await getBenchmarkAnalysisTab();
        console.log('对标分析标签信息', res.data.data);
        
        if (res.data && res.data.data && res.data.data.length > 0) {
            // 根据API数据动态设置主标签页
            mainTabs.value = res.data.data.map(item => ({
                name: item.groupName,
                id: item.id,
                groupSort: item.groupSort
            }));
            
            // 根据groupSort排序
            mainTabs.value.sort((a, b) => a.groupSort - b.groupSort);
        } else {
            // 如果API返回空数据，使用默认数据
            setDefaultTabs();
        }
    } catch (error) {
        console.error('获取对标分析标签信息失败:', error);
        // 如果API失败，使用默认的标签页数据
        setDefaultTabs();
    }
}

// 设置默认标签页数据
const setDefaultTabs = () => {
    mainTabs.value = [
        { name: '我的对标', id: 'my_benchmarking_group', groupSort: 1 },
        { name: '行业对标', id: 'industry_benchmarking_group', groupSort: 2 },
        { name: '区域对标', id: 'area_benchmarking_group', groupSort: 3 }
    ];
};

// 加载所有tab的对标企业配置数据
const loadAllTabsCompanyConfigData = async () => {
    try {
        // 为每个标签页加载企业配置数据
        for (let i = 0; i < mainTabs.value.length; i++) {
            const tab = mainTabs.value[i];
            const params = {
                entId: userInfo.outCompCode || '',
                entOrgCode: userInfo.orgCode || '',
                groupId: tab.id
            };

            try {
                const res = await getBenchmarkCompanyConfigList(params);
                console.log(`${tab.name}企业配置数据:`, res.data.data);

                if (res.data && res.data.data && res.data.data.length > 0) {
                    // 先移除该分组的旧数据
                    allCompanyConfigs.value = allCompanyConfigs.value.filter(item => item.groupId !== tab.id);
                    // 添加新数据，保持原始数据结构
                    const newCompanies = res.data.data.map(item => ({
                        ...item // 保持完整的原始数据结构
                    }));
                    allCompanyConfigs.value.push(...newCompanies);
                }
            } catch (error) {
                console.error(`加载${tab.name}企业配置失败:`, error);
            }
        }

        console.log('所有tab企业配置加载完成:', allCompanyConfigs.value);
    } catch (error) {
        console.error('加载企业配置数据失败:', error);
        uni.showToast({
            title: '获取企业配置失败',
            icon: 'none'
        });
    }
};



// ==================== 常量定义 ====================
const filterNames = {
    companies: '对标企业',
    bondTypes: '债券类型'
};

// 获取当前选中标签的ID
const getCurrentGroupId = () => {
    if (mainTabs.value.length > 0 && mainTabs.value[currentMainTab.value]) {
        return mainTabs.value[currentMainTab.value].id;
    }
    // 如果没有数据，返回默认值
    const defaultGroupIds = ['my_benchmarking_group', 'industry_benchmarking_group', 'area_benchmarking_group'];
    return defaultGroupIds[currentMainTab.value] || 'my_benchmarking_group';
};

// ==================== Store实例 ====================
const permissionStore = usePermissionStore();
const userInfo = permissionStore.getUserInfo;
// ==================== 标签页数据定义 ====================
// 主标签页数据 - 改为响应式数据，初始为空数组
const mainTabs = ref([]);

// 底部标签页数据
const bottomTabs = ref([
    { name: '存续对标' },
    { name: '发行对标' },
    { name: '财务对标' }
]);

// 分析类型标签数据
const analysisTabs = ref([
    { name: '指标对比' },
    { name: '趋势分析' }
]);

// ==================== 筛选选项数据定义 ====================
// 不同底部标签页对应的筛选选项
const filterOptionsMap = ref({
    0: [ // 存续对标
        { name: filterNames.companies },
        { name: filterNames.bondTypes }
    ],
    1: [ // 发行对标
        { name: '发行日期' },
        { name: filterNames.companies },
        { name: filterNames.bondTypes },
        { name: '期限' }
    ],
    2: { // 财务对标 - 根据分析类型显示不同选项
        0: [ // 指标对比
            { name: '报告期' },
            { name: filterNames.companies }
        ],
        1: [ // 趋势分析
            { name: filterNames.companies },
            { name: '指标选择' },
            { name: '报告时间' },
            { name: '报告类型' }
        ]
    }
});

// 条件选项数据
const conditionItemsMap = ref({
    // 财务对标下根据分析类型显示不同的条件选项
    2: {
        0: [ // 指标对比
            { name: '总资产（万元）' },
            { name: '总负债（万元）' },
            { name: '净资产（万元）' },
            { name: '流动资产（万元）' },
            { name: '非流动资产（万元）' },
            { name: '应收账款（万元）' },
            { name: '流动负债（万元）' },
            { name: '非流动负债（万元）' },
            { name: '应付债券（万元）' },
            { name: '股东权益（万元）' },
            { name: '营业利润（万元）' },
            { name: '净利润（万元）' },
            { name: '资产负债率（%）' },
            { name: '速动比率（%）' },
            { name: '流动比率（%）' }
        ],
        1: [ // 趋势分析
            { name: '总资产（万元）' },
            { name: '总负债（万元）' },
            { name: '净资产（万元）' },
            { name: '流动负债（万元）' },
            { name: '非流动负债（万元）' },
            { name: '应收账款（万元）' },
            { name: '流动资产（万元）' },
            { name: '非流动资产（万元）' },
            { name: '应付债券（万元）' },
            { name: '股东权益（万元）' },
            { name: '营业利润（万元）' },
            { name: '净利润（万元）' },
            { name: '资产负债率（%）' },
            { name: '净利率（%）' },
            { name: '速动比率(%)' },
            { name: '流动比率(%)' }
        ]
    }
});

// ==================== 企业和状态数据 ====================
// 对标企业配置数据 - 改为统一存储所有企业数据
const allCompanyConfigs = ref([]);

// ==================== 标签选择状态 ====================
// 当前选中的各个tab索引（独立控制）
const currentMainTab = ref(0);
const currentBottomTab = ref(0);
const currentAnalysisTab = ref(0); // 默认选中指标对比
const currentCondition = ref(0);

// ==================== 弹窗相关状态 ====================
const companyPopup = ref(null);
const bondTypePopup = ref(null);
const reportPeriodPopup = ref(null);
const financialIndicatorsPopup = ref(null);
const showDatePicker = ref(false); // 控制日期选择器的显示状态

// 对标企业选择相关数据
const companyOptions = ref([]);
const selectedCompanies = ref([]);

// 债券类型选择相关数据
const bondTypeOptions = ref([]);
const selectedBondTypes = ref([]);

// 报告期选择相关数据
const reportPeriodOptions = ref([]);
const selectedReportPeriod = ref(null);

// 财务对标指标选择相关数据（多选）
const financialIndicatorsOptions = ref([]);
const selectedFinancialIndicators = ref([]);

// 日期范围选择结果
const dateRange = ref({
    startDate: '',
    endDate: ''
});

// 筛选选择结果
const filterSelections = ref({
    companies: [],
    bondTypes: [],
    dateRange: []
});

// ==================== 计算属性 ====================
// 获取当前企业列表 - 根据当前选中的tab过滤企业
const currentCompanyList = computed(() => {
    const currentGroupId = getCurrentGroupId();
    return allCompanyConfigs.value.filter(item => item.groupId === currentGroupId) || [];
});

// 根据当前底部标签页获取对应的筛选选项
const currentFilterOptions = computed(() => {
    // 财务对标需要根据分析类型来决定显示的筛选选项
    if (currentBottomTab.value === 2) {
        return filterOptionsMap.value[2][currentAnalysisTab.value] || [];
    }
    return filterOptionsMap.value[currentBottomTab.value] || [];
});

// 根据当前底部标签页和分析类型获取对应的条件选项
const currentConditionItems = computed(() => {
    // 只有财务对标才有条件选项
    if (currentBottomTab.value === 2) {
        return conditionItemsMap.value[2][currentAnalysisTab.value] || [];
    }
    return [];
});

// 判断是否显示分析类型切换标签
const shouldShowAnalysisTabs = computed(() => {
    // 只有财务对标（索引2）显示分析标签，存续对标和发行对标都不显示
    return currentBottomTab.value === 2;
});

// 判断是否显示对标列表
const shouldShowBenchmarkList = computed(() => {
    // 如果不是财务对标，始终显示列表
    if (currentBottomTab.value !== 2) {
        return true;
    }
    // 如果是财务对标，只在指标对比时显示，趋势分析时不显示
    return currentAnalysisTab.value === 0;
});

// 对标列表标题
const benchmarkListTitle = computed(() => {
    // 如果不是财务对标，显示默认标题
    if (currentBottomTab.value !== 2) {
        return '对标列表';
    }
    // 如果是财务对标且是指标对比，显示指标对比列表
    if (currentAnalysisTab.value === 0) {
        return '指标对比列表';
    }
    // 其他情况（理论上不会显示）
    return '对标列表';
});

// ==================== 工具函数 ====================

// 处理过长的名称
const truncateName = (name, maxLength = 8) => {
    if (name.length <= maxLength) {
        return name;
    }
    return name.substring(0, maxLength) + '...';
};

// ==================== 标签切换方法 ====================
// 切换主标签页
const switchMainTab = (index) => {
    currentMainTab.value = index;
    // 切换标签页时只更新下拉列表选项，企业配置数据已在初始化时全部加载
    loadCompanyOptions();
};

// 切换底部标签页
const switchBottomTab = (index) => {
    currentBottomTab.value = index;
    // 如果切换到财务对标，默认选中指标对比
    if (index === 2) {
        currentAnalysisTab.value = 0;
    }
};

// 切换分析标签
const switchAnalysisTab = (index) => {
    currentAnalysisTab.value = index;
};

// 选择条件
const selectCondition = (index) => {
    currentCondition.value = index;
};

// ==================== 企业管理方法 ====================
// 添加企业
const addCompany = () => {
    const currentGroupId = getCurrentGroupId();
    const currentTabName = mainTabs.value[currentMainTab.value]?.name || '我的对标';

    uni.navigateTo({
        url: `/subPageB/add-company/add-company?groupId=${currentGroupId}&tabName=${encodeURIComponent(currentTabName)}`
    });
};

// 删除企业
const deleteCompany = async (index) => {
    try {
        const currentList = currentCompanyList.value;
        if (index >= 0 && index < currentList.length) {
            const companyToDelete = currentList[index];
            
            // 从allCompanyConfigs中移除该企业
            const globalIndex = allCompanyConfigs.value.findIndex(item => item.id === companyToDelete.id);
            if (globalIndex > -1) {
                allCompanyConfigs.value.splice(globalIndex, 1);
            }
            
            // 这里可以调用删除企业的API
            // await deleteCompanyAPI(companyToDelete.id);
            
            uni.showToast({
                title: '删除成功',
                icon: 'success'
            });
        }
    } catch (error) {
        console.error('删除企业失败:', error);
        uni.showToast({
            title: '删除失败',
            icon: 'none'
        });
    }
};

// ==================== 筛选下拉方法 ====================
// 打开下拉选择
const toggleDropdown = (index) => {
    const filterName = currentFilterOptions.value[index].name;

    if (filterName === filterNames.companies) {
        openCompanyPopup();
    } else if (filterName === filterNames.bondTypes) {
        openBondTypePopup();
    } else if (filterName === '发行日期') {
        showDatePicker.value = true;
    } else if (filterName === '报告期') {
        openReportPeriodPopup();
    } else if (filterName === '指标选择') {
        openFinancialIndicatorsPopup();
    } else {
        uni.showToast({
            title: `${filterName}功能开发中`,
            icon: 'none'
        });
    }
};

// ==================== 日期选择相关方法 ====================
// 处理日期选择变更
const handleDateRangeChange = (dateArr) => {
    dateRange.value.startDate = dateArr[0];
    dateRange.value.endDate = dateArr[1];

    // 更新筛选选择结果
    filterSelections.value.dateRange = dateArr;

    uni.showToast({
        title: `已选择日期: ${dateArr[0]} 至 ${dateArr[1]}`,
        icon: 'none'
    });

    // 这里可以添加数据刷新的逻辑
    // refreshData();
};

// ==================== 对标企业弹窗相关方法 ====================
const openCompanyPopup = async () => {
    await loadCompanyOptions();
    if (companyPopup.value) {
        companyPopup.value.open();
    }
};

const closeCompanyPopup = () => {
    if (companyPopup.value) {
        companyPopup.value.close();
    }
};

// 加载对标企业下拉列表
const loadCompanyOptions = async () => {
    try {
        const params = {
            entId: userInfo.outCompCode || '', // 从store获取企业ID
            entOrgCode: userInfo.orgCode || '', // 从store获取企业组织代码
            groupId: getCurrentGroupId() // 使用动态获取的groupId
        };

        const res = await getBenchmarkCompanySelectList(params);
        console.log('res', res);
        if (res.data && res.data.data.length > 0) {
            companyOptions.value = res.data.data || [];
        } else {
            companyOptions.value = [];
        }
    } catch (error) {
        console.error('加载对标企业列表失败:', error);
        uni.showToast({
            title: '加载企业列表失败',
            icon: 'none'
        });
    }
};

const isCompanySelected = (company) => {
    return selectedCompanies.value.some(item => item.bmEntId === company.bmEntId);
};

const toggleCompanySelection = (company) => {
    const index = selectedCompanies.value.findIndex(item => item.bmEntId === company.bmEntId);
    if (index > -1) {
        selectedCompanies.value.splice(index, 1);
    } else {
        selectedCompanies.value.push(company);
    }
};

// 全选所有对标企业
const selectAllCompanies = () => {
    selectedCompanies.value = [...companyOptions.value];
};

// 清空对标企业选择
const clearCompanies = () => {
    selectedCompanies.value = [];
};

// 反选对标企业
const invertCompanies = () => {
    const newSelection = [];
    companyOptions.value.forEach(company => {
        if (!isCompanySelected(company)) {
            newSelection.push(company);
        }
    });
    selectedCompanies.value = newSelection;
};

const confirmCompanySelection = () => {
    // 更新筛选选择结果
    filterSelections.value.companies = selectedCompanies.value.map(item => item.bmEntName);

    closeCompanyPopup();

    uni.showToast({
        title: `已选择${selectedCompanies.value.length}家企业`,
        icon: 'none'
    });
};

// ==================== 债券类型弹窗相关方法 ====================
const openBondTypePopup = async () => {
    await loadBondTypeOptions();
    if (bondTypePopup.value) {
        bondTypePopup.value.open();
    }
};

const closeBondTypePopup = () => {
    if (bondTypePopup.value) {
        bondTypePopup.value.close();
    }
};

const loadBondTypeOptions = async () => {
    try {
        const res = await getBondType();
        console.log('bondType res', res);
        if (res.data && res.data.data && res.data.data.length > 0) {
            bondTypeOptions.value = res.data.data || [];
        } else {
            bondTypeOptions.value = [];
        }
    } catch (error) {
        console.error('加载债券类型失败:', error);
        bondTypeOptions.value = [];
        uni.showToast({
            title: '加载债券类型失败',
            icon: 'none'
        });
    }
};

const isBondTypeSelected = (bondType) => {
    return selectedBondTypes.value.some(item => item.bondTypeCode === bondType.bondTypeCode);
};

const toggleBondTypeSelection = (bondType) => {
    const index = selectedBondTypes.value.findIndex(item => item.bondTypeCode === bondType.bondTypeCode);
    if (index > -1) {
        selectedBondTypes.value.splice(index, 1);
    } else {
        selectedBondTypes.value.push(bondType);
    }
};

// 全选所有债券类型
const selectAllBondTypes = () => {
    const allTypes = [];
    bondTypeOptions.value.forEach(category => {
        category.list.forEach(subType => {
            allTypes.push(subType);
        });
    });
    selectedBondTypes.value = allTypes;
};

// 清空债券类型选择
const clearBondTypes = () => {
    selectedBondTypes.value = [];
};

// 反选债券类型
const invertBondTypes = () => {
    const allTypes = [];
    bondTypeOptions.value.forEach(category => {
        category.list.forEach(subType => {
            if (!isBondTypeSelected(subType)) {
                allTypes.push(subType);
            }
        });
    });
    selectedBondTypes.value = allTypes;
};

const resetBondTypeSelection = () => {
    selectedBondTypes.value = [];
};

const confirmBondTypeSelection = () => {
    // 更新筛选选择结果
    filterSelections.value.bondTypes = selectedBondTypes.value.map(item => item.bondTypeName);

    closeBondTypePopup();

    uni.showToast({
        title: `已选择${selectedBondTypes.value.length}种债券类型`,
        icon: 'none'
    });
};

// ==================== 报告期弹窗相关方法 ====================
const openReportPeriodPopup = async () => {
    if (reportPeriodPopup.value) {
        reportPeriodPopup.value.open();
    }
};

const closeReportPeriodPopup = () => {
    if (reportPeriodPopup.value) {
        reportPeriodPopup.value.close();
    }
};

const isReportPeriodSelected = (period) => {
    if (!selectedReportPeriod.value) return false;
    return selectedReportPeriod.value.reportCode === period.reportCode &&
           selectedReportPeriod.value.reportYear === period.reportYear;
};

const selectReportPeriod = (period) => {
    selectedReportPeriod.value = period;
};

const confirmReportPeriodSelection = () => {
    closeReportPeriodPopup();

    if (selectedReportPeriod.value) {
        uni.showToast({
            title: `已选择${selectedReportPeriod.value.reportYear}年${selectedReportPeriod.value.reportName}`,
            icon: 'none'
        });
    }
};

// ==================== 财务指标弹窗相关方法 ====================
const openFinancialIndicatorsPopup = async () => {
    if (financialIndicatorsPopup.value) {
        financialIndicatorsPopup.value.open();
    }
};

const closeFinancialIndicatorsPopup = () => {
    if (financialIndicatorsPopup.value) {
        financialIndicatorsPopup.value.close();
    }
};

const isFinancialIndicatorSelected = (indicator) => {
    return selectedFinancialIndicators.value.some(item => item.fieldCode === indicator.fieldCode);
};

const toggleFinancialIndicatorSelection = (indicator) => {
    const index = selectedFinancialIndicators.value.findIndex(item => item.fieldCode === indicator.fieldCode);
    if (index > -1) {
        selectedFinancialIndicators.value.splice(index, 1);
    } else {
        selectedFinancialIndicators.value.push(indicator);
    }
};

// 全选所有财务指标
const selectAllFinancialIndicators = () => {
    selectedFinancialIndicators.value = [...financialIndicatorsOptions.value];
};

// 清空财务指标选择
const clearFinancialIndicators = () => {
    selectedFinancialIndicators.value = [];
};

// 反选财务指标
const invertFinancialIndicators = () => {
    const newSelection = [];
    financialIndicatorsOptions.value.forEach(indicator => {
        if (!isFinancialIndicatorSelected(indicator)) {
            newSelection.push(indicator);
        }
    });
    selectedFinancialIndicators.value = newSelection;
};

const confirmFinancialIndicatorsSelection = () => {
    closeFinancialIndicatorsPopup();

    uni.showToast({
        title: `已选择${selectedFinancialIndicators.value.length}个财务指标`,
        icon: 'none'
    });
};

// ==================== 对标列表数据 ====================
const benchmarkListData = ref([
    {
        objectId: '1',
        name: '淄博市城市资产运营有限公司',
        rate: '否',
        change: '4',
        changeType: ''
    },
    {
        objectId: '2',
        name: '宁波太平鸟时尚服饰股份有限公司',
        rate: '否',
        change: '1',
        changeType: ''
    },
    {
        objectId: '3',
        name: '李宁有限公司',
        rate: '否',
        change: '1',
        changeType: ''
    }
]);

// 对标列表表格配置
const benchmarkTableColumns = ref([
    {
        name: 'name',
        label: '发行人',
        fixed: true,
        emptyString: '--',
        width: 240,
        align: 'left'
    },
    {
        name: 'rate',
        label: '是否城投',
        emptyString: '--',
        align: 'center'
    },
    {
        name: 'change',
        label: '债券只数',
        emptyString: '--',
        align: 'center'
    }
]);

// 对标列表加载状态
const benchmarkListLoading = ref(false);
const benchmarkHasMore = ref(true);

// ==================== 表格样式和操作方法 ====================
// 单元格样式函数
const benchmarkCellStyle = ({ row, column, rowIndex, columnIndex }) => {
    // 第一列特殊处理：显示完整文字
    if (columnIndex === 0) {
        return {
            width: 'auto',
            minWidth: '200rpx',
            whiteSpace: 'normal',
            overflow: 'visible',
            textOverflow: 'unset',
            wordBreak: 'break-word',
            lineHeight: '1.4'
        };
    }
    return {};
};

// 加载更多对标数据
const loadMoreBenchmarkData = () => {
    if (benchmarkListLoading.value || !benchmarkHasMore.value) {
        return;
    }

    benchmarkListLoading.value = true;

    // 模拟加载更多数据
    setTimeout(() => {
        const newData = [
            {
                objectId: `${benchmarkListData.value.length + 1}`,
                name: '示例企业A',
                rate: '是',
                change: '2',
                changeType: ''
            },
            {
                objectId: `${benchmarkListData.value.length + 2}`,
                name: '示例企业B',
                rate: '否',
                change: '3',
                changeType: ''
            }
        ];

        benchmarkListData.value.push(...newData);
        benchmarkListLoading.value = false;

        // 模拟数据加载完毕
        if (benchmarkListData.value.length >= 10) {
            benchmarkHasMore.value = false;
        }
    }, 1000);
};

// 显示对标详情
const showBenchmarkDetail = (row, column, rowIndex, colIndex) => {
    // 跳转到详情页或显示详情信息
    uni.showToast({
        title: `查看${row.name}详情`,
        icon: 'none'
    });
};

// 处理点击加载更多按钮
const handleLoadMoreClick = () => {
    loadMoreBenchmarkData();
};

// ==================== 图表数据（独立模块，暂时断开与企业数据的关联）====================
// 图表组件引用
const mixedChart = ref(null);

// 模拟图表数据 - 使用固定假数据，不依赖企业配置
const mockChartBarData = ref([
    { name: '资产总额', data: [12000, 9500, 15000, 13200, 18500, 16700, 9800, 14300] },
    { name: '负债总额', data: [8000, 6500, 10000, 8800, 12000, 9500, 5500, 9200] }
]);

const mockChartLineData = ref([
    { name: '资产增长率', data: [15, 12, 18, 16, 22, 19, 13, 17] },
    { name: '负债增长率', data: [10, 8, 13, 11, 15, 12, 9, 11] }
]);

// 模拟X轴标签 - 使用固定企业名称，不依赖企业配置
const mockChartXLabels = ref([
    '本企业', '企业A', '企业B', '企业C', '企业D', '企业E', '企业F', '企业G'
]);

// ==================== 原始图表数据逻辑（已断开，待后续整合）====================
// TODO: 后续需要将图表数据与企业配置关联时，使用以下逻辑
/*
const chartBarData = ref([
    { name: '资产总额', data: [12000, 9500, 15000, 13200, 18500, 16700, 9800, 14300] },
    { name: '负债总额', data: [8000, 6500, 10000, 8800, 12000, 9500, 5500, 9200] }
]);

const chartLineData = ref([
    { name: '资产增长率', data: [15, 12, 18, 16, 22, 19, 13, 17] },
    { name: '负债增长率', data: [10, 8, 13, 11, 15, 12, 9, 11] }
]);

const chartXLabels = computed(() => {
    return currentCompanyList.value.map(item => truncateName(item.bmEntName || item.entName || '未知企业'));
});
*/
</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    /* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
    flex: 1;
    margin-top: 160rpx;
    padding: 20rpx 0;
    padding-top: 0;
    overflow: auto;
    /* 主滚动容器 */
    position: relative;
}

/* 1.4 滚动区域 */
.scrollable-content {
    height: 100%;
    -webkit-overflow-scrolling: touch;
    /* 增加iOS滚动平滑效果 */

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

/* 2.1 标签页切换 */
.tab-container {
    display: flex;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    /* 给固定头部留出空间 */
}

.tab-item {
    flex: 1;
    padding: 30rpx 0;
    text-align: center;
    position: relative;
    font-size: 32rpx;
    color: #999;
    transition: all 0.3s ease;
    font-weight: bold;
}

.tab-item.active {
    color: #FF9900;
    font-weight: bold;
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40%;
    height: 6rpx;
    background-color: #FF9900;
    border-radius: 6rpx 6rpx 0 0;
}

/* 加载状态提示 */
.loading-tabs {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60rpx 0;
    margin-bottom: 20rpx;
}

.loading-text {
    font-size: 28rpx;
    color: #999;
}

/* 2.2 对标企业配置卡片 */
.benchmark-card {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    padding: 10rpx;

    .bond-title {
        display: flex;
        align-items: center;

        .title-icon {
            width: 48rpx;
            height: 52rpx;
            position: relative;
            top: -10rpx;

            &::before {
                content: '';
                position: absolute;
                inset: 0;
                background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
            }
        }

        .title-text {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            transform: translateX(-25rpx);
        }
    }
}

.card-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
}

.add-btn {
    display: flex;
    color: #FF9900;
    font-size: 32rpx;

    text {
        margin-left: 6rpx;
    }
}

.company-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.company-item {
    width: 48%;
    background-color: #f9f9f9;
    border-radius: 10rpx;
    position: relative;
    height: 100rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    margin-bottom: 20rpx;
    box-sizing: border-box;
}

.company-content {
    flex: 1;
    overflow: hidden;
}

.company-name {
    font-size: 26rpx;
    color: #333;
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.delete-icon {
    color: #999;
    font-size: 30rpx;
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 无企业数据提示 */
.no-company-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60rpx 0;
    width: 100%;
}

.no-company-text {
    font-size: 28rpx;
    color: #999;
}

/* 2.3 底部标签页 */
.bottom-tabs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30rpx;
    padding: 0;
}

.bottom-tab-item {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    color: #666;
    padding: 25rpx 0;
    position: relative;
    background-color: #fff;
    margin: 0 10rpx;
    border-radius: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.bottom-tab-item:first-child {
    margin-left: 0;
}

.bottom-tab-item:last-child {
    margin-right: 0;
}

.bottom-tab-item.active {
    color: #fff;
    background-image: linear-gradient(to right, #f9b649, #f09339);
    font-weight: bold;
}

.tab-arrow {
    position: absolute;
    bottom: -15rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15rpx solid transparent;
    border-right: 15rpx solid transparent;
    border-top: 15rpx solid #f1a65e;
}

/* 3.1 指标/趋势分析切换样式 */
.analysis-tab-container {
    display: flex;
    position: relative;
    margin-bottom: 40rpx;
    border-radius: 10rpx;
    width: 100%;
}

.analysis-tab-item {
    flex: 1;
    padding: 30rpx 0;
    padding-top: 0;
    text-align: center;
    position: relative;
    font-size: 32rpx;
    color: #999;
    transition: all 0.3s ease;
    font-weight: bold;
}

.analysis-tab-item.active {
    color: #FF8E2B;
    font-weight: bold;
}

.analysis-tab-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30%;
    height: 6rpx;
    background-color: #FF9900;
    border-radius: 6rpx 6rpx 0 0;
}

/* 3.2 筛选条件下拉区域 */
.filter-dropdown-container {
    display: flex;
    justify-content: center;
    margin-bottom: 30rpx;
}

.filter-dropdown-item {
    font-size: 28rpx;
    color: #333;
    display: flex;
    align-items: center;
    width: 165rpx;
    justify-content: center;
}

.dropdown-icon {
    margin-left: 6rpx;
    font-size: 20rpx;
    color: #999;
}

/* 3.3 条件单选区域(横向滚动) */
.condition-scroll {
    white-space: nowrap;
    margin-bottom: 30rpx;
}

.condition-container {
    display: inline-flex;
}

.condition-item {
    display: inline-block;
    padding: 16rpx 40rpx;
    margin-right: 20rpx;
    background-color: #f5f5f5;
    border-radius: 50rpx;
    font-size: 28rpx;
    color: #666;
    white-space: nowrap;
}

.condition-item.active {
    background-image: linear-gradient(to right, #f9b649, #f09339);
    color: #fff;
}

/* 2.4 图表卡片 */
.chart-card {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
}

.filter-row {
    display: flex;
    margin-bottom: 20rpx;
}

.filter-item {
    display: flex;
    align-items: center;
    margin-right: 30rpx;
    font-size: 26rpx;
    color: #666;
}

.chart-container {
    width: 100%;
    /* 使用固定宽度，避免滚动时宽度变化 */
    margin: 0 auto 20rpx;
    /* 居中显示 */
    position: relative;
    transform: translateZ(0);
    /* 开启硬件加速 */
    backface-visibility: hidden;
    /* 防止闪烁 */
    perspective: 1000;
    will-change: transform;
    /* 提示浏览器该元素会发生变化，进行优化 */
    overflow: hidden;
    /* 防止溢出 */
    display: flex;
    flex-direction: column;
}

.chart-desc {
    font-size: 24rpx;
    color: #999;
    padding: 0 20rpx;
    /* 添加内边距，确保文字不会溢出 */
}

/* 2.5 对标列表 */
.benchmark-table-card {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 10rpx;
}

.title-wrapper {
    display: flex;
    align-items: center;
}

.title-icon {
    width: 48rpx;
    height: 52rpx;
    position: relative;
    top: -10rpx;

    &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
        clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    }
}

.title-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    transform: translateX(-25rpx);
}

.bonds-list {
    padding: 10rpx 0;
    position: relative;
}

/* 加载更多按钮样式 */
.view-more {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: 30rpx;
    cursor: pointer;
}

.view-more:active {
    background-color: #f0f0f0;
}

.view-more-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.icon-arrow {
    width: 24rpx;
    height: 26rpx;
    margin-bottom: 10rpx;
}

.more-text {
    font-size: 28rpx;
    color: #666;
}

.loading-text {
    font-size: 28rpx;
    color: #999;
}

/* 无数据提示样式 */
.no-data-tip {
    padding: 80rpx 0;
    text-align: center;
}

.no-data-text {
    font-size: 28rpx;
    color: #999;
}

/* 没有更多数据提示样式 */
.no-more-tip {
    padding: 30rpx 0;
    text-align: center;
}

.no-more-text {
    font-size: 24rpx;
    color: #999;
}

/* 底部弹窗样式 */
.bottom-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    animation: fadeIn 0.3s ease-in-out;
}

.bottom-popup-content {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    width: 100%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease-in-out;
    overflow: hidden;
}

.popup-handle {
    width: 60rpx;
    height: 8rpx;
    background-color: #e0e0e0;
    border-radius: 4rpx;
    margin: 20rpx auto 0;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.popup-close {
    padding: 10rpx;
    color: #999;
}

.popup-body {
    flex: 1;
    overflow-y: auto;
    padding: 0 30rpx;
    max-height: 60vh;
}

.popup-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1rpx solid #f0f0f0;
    padding: 20rpx 30rpx;
    background-color: #fff;
}

.selected-count {
    font-size: 28rpx;
    color: #666;
}

.popup-buttons {
    display: flex;
    gap: 20rpx;
}

.popup-btn {
    padding: 16rpx 32rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    text-align: center;
    min-width: 120rpx;
}

.reset-btn {
    background-color: #f5f5f5;
    color: #666;
}

.confirm-btn {
    background-color: #FF9900;
    color: #fff;
}

/* 选项列表样式 */
.option-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
}

.option-item:last-child {
    border-bottom: none;
}

.option-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.option-name {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.option-desc {
    font-size: 24rpx;
    color: #999;
    margin-top: 4rpx;
}

.option-checkbox {
    margin-left: 20rpx;
}

.checkbox {
    width: 40rpx;
    height: 40rpx;
    border: 2rpx solid #ddd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.checkbox.checked {
    background-color: #FF9900;
    border-color: #FF9900;
}

/* 债券类型分类样式 */
.category-group {
    margin-bottom: 30rpx;
}

.category-header {
    padding: 20rpx 0 15rpx;
    border-bottom: 2rpx solid #FF9900;
    margin-bottom: 20rpx;
}

.category-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
}

/* 无数据提示样式 */
.no-data-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 100rpx 0;
}

.no-data-text {
    font-size: 28rpx;
    color: #999;
}

/* 选项容器滚动样式 */
.company-options,
.bond-type-options {
    padding-bottom: 20rpx;
}

.company-options::-webkit-scrollbar,
.bond-type-options::-webkit-scrollbar {
    width: 6rpx;
}

.company-options::-webkit-scrollbar-thumb,
.bond-type-options::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 6rpx;
}

.company-options::-webkit-scrollbar-track,
.bond-type-options::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 6rpx;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

/* 弹窗样式 */
.popup-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 30rpx 30rpx 0;
    max-height: 70vh;
    display: flex;
    flex-direction: column;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;
    border-bottom: 2rpx solid #F5F5F5;
}

.popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.close-icon {
    padding: 10rpx;
}

.popup-options {
    flex: 1;
    overflow-y: auto;
    padding: 20rpx 0;
    max-height: 50vh;
}

.options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
}

.options-header text {
    font-size: 26rpx;
    color: #999;
}

.options-actions {
    display: flex;
    gap: 30rpx;
}

.options-actions text {
    color: #FF8E2B;
    font-size: 26rpx;
}

.options-actions text:active {
    opacity: 0.8;
}

.options-list {
    padding: 20rpx 0;
}

.category-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    padding: 20rpx 0 10rpx;
    border-bottom: 1rpx solid #f0f0f0;
    margin-bottom: 10rpx;
}

.category-options {
    display: flex;
    flex-wrap: wrap;
}

.option-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    width: 50%;
    flex-shrink: 0;
}

.checkbox {
    width: 36rpx;
    height: 36rpx;
    border-radius: 6rpx;
    border: 2rpx solid #DCDFE6;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.checkbox.checked {
    background-color: #FF8E2B;
    border-color: #FF8E2B;
}

.option-label {
    font-size: 28rpx;
    color: #333;
    flex: 1;
}

.popup-footer {
    padding: 30rpx;
    border-top: 2rpx solid #F5F5F5;
}

.confirm-btn {
    width: 100%;
    height: 88rpx;
    background: #FF8E2B;
    border-radius: 44rpx;
    color: #FFFFFF;
    font-size: 32rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
}

.confirm-btn:active {
    opacity: 0.9;
}

/* 报告期弹窗单选样式 */
.radio {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #DCDFE6;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.radio.checked {
    border-color: #FF8E2B;
}

.radio-dot {
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background-color: #FF8E2B;
}

/* 财务指标弹窗多选样式 */
.checkbox {
    width: 36rpx;
    height: 36rpx;
    border-radius: 8rpx;
    border: 2rpx solid #DCDFE6;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.checkbox.checked {
    border-color: #FF8E2B;
    background-color: #FF8E2B;
}

.options-actions {
    display: flex;
    gap: 30rpx;
    margin-left: auto;
}

.options-actions text {
    color: #FF8E2B;
    font-size: 28rpx;
    cursor: pointer;
}

.options-actions text:active {
    opacity: 0.7;
}
</style>